# 视频分析系统功能需求文档

## 1. 算法功能需求

### 1.1 基础算法支持
- 支持Yolo8-Seg分割算法和Yolo11-Seg分割算法
- 支持Yolo8-obb和Yolo11-obb算法模型
- 支持Yolo8-pose模型和关键点渲染
- 支持ResNet分类算法
- 支持XcFaceNet人脸特征提取算法
- 支持TensorRT推理引擎（Yolo/ResNet/FaceNet/DeepSort等）
- 支持OpenVINO推理引擎
- 支持OnnxRuntime推理引擎
- 支持RKNPU推理引擎（适用于RK芯片）
- 支持昇腾推理引擎

### 1.2 行为算法功能
- AREA/STAY/LEAVE行为算法（兼容分割算法）
- CROSSCOUNT行为算法（过滤相同ID重复触发）
- SUPER行为算法（支持配置目标任意位置作为计算中心点）
- 越线检测算法（检测目标逆行）
- 周界入侵行为算法
- 打架行为算法
- 暴力行为算法
- 目标计数算法
- OVERLAP重叠后处理行为算法
- 徘徊逗留后处理行为算法
- 无人值守算法后处理
- 离岗检测算法后处理
- COUNT计数后处理行为算法
- 火焰报警监测功能
- 烟雾报警监测功能
- PLATE行为算法（车牌相关）
- 背景分类算法（背景过滤）
- APIv2类型行为算法（结合API和内置算法）

### 1.3 视频质量检测算法
- 遮挡检测算法
- 灰屏检测算法
- 花屏检测算法

### 1.4 OCR识别算法
- 车牌识别算法
- 包装日期识别算法
- 表单识别算法
- 通用卡证识别算法

### 1.5 算法流程模式
- 模式1：检测算法>>行为算法
- 模式2：检测算法>>追踪算法>>行为算法
- 模式3：检测算法>>分类算法>>行为算法
- 模式4：分类算法>>行为算法
- 模式5：行为算法
- 模式6：分类算法->检测算法->行为算法
- 模式7：检测算法->分类算法->特征算法->行为算法

## 2. 视频接入功能需求

### 2.1 视频流协议支持
- 支持gb28181视频流接入协议
- 支持rtsps/rtmps/https等ssl加密协议
- 支持h265视频流播放
- 支持MJpg流播放
- 支持接收音频功能
- 支持被动接收推流时自动新增对应数据
- 支持手动开启转发和手动停止转发

### 2.2 gb28181功能
- SIP注册兼容多种注册信息加密算法
- 支持TCP模式和公网NAT网络环境
- 支持接入多通道录像机
- 支持云台控制功能
- 提供云台开放接口

### 2.3 视频解码功能
- 支持视频解码复用技术
- 支持硬件解码和硬编码
- 支持英特尔集显qsv硬解码
- 支持跳帧解码功能
- 支持丢弃循环滤波和IDCT
- 支持可配置视频解码线程数量
- 支持MPP视频硬解码加速（RK版）
- 支持RGA加速预处理（RK版）
- 支持可配置硬件解码路数和硬编码路数

### 2.4 视频流重试功能
- 拉流重试功能（中途中断视频流重试）
- 解码重试功能（解码器故障重试）
- 无限次重试拉流和推流
- 连续读流/解码失败日志记录

## 3. 人脸识别功能需求

### 3.1 人脸检测与识别
- 人脸识别功能（支持人脸图片质量过滤）
- 人脸特征库初始化功能
- 支持可配置陌生人报警
- 人脸识别行为算法

### 3.2 人脸管理接口
- 添加人脸/删除人脸开放接口
- 查询所有人脸/根据图片查询人脸接口
- 人脸算法开启/关闭接口

## 4. 语音识别功能需求

### 4.1 语音处理
- 音频解码和音频重采样格式兼容
- 内置推理引擎进行语音识别
- 语音识别功能（大规模重构分析器核心模块）

## 5. 录像与存储功能需求

### 5.1 录像计划功能
- 录像计划功能（可设置录像天数）
- 录像回放功能
- 录音功能支持
- 手动录像功能
- 手动抓拍图片功能

### 5.2 录像管理接口
- 添加录像计划/删除录像计划接口
- 查询录像计划接口
- 查询录像文件列表接口
- 查询录像文件播放地址接口

### 5.3 存储管理
- 报警数据和录像数据按存储空间上限自动覆盖
- 可配置存储根路径
- 自动定时清理日志和缓存功能
- 手动清理日志缓存功能

## 6. 报警管理功能需求

### 6.1 报警处理
- 实时报警功能
- 报警视频合成功能
- 报警图片合成（支持画框/不画框/画框+不画框三种类型）
- 报警过滤器功能
- 强制触发报警功能
- 邮件通知功能
- 报警审核功能

### 6.2 报警数据管理
- 报警数据查询/删除/清理缓存接口
- 报警数据导出为样本接口
- 报警数据上传接口（支持kafka，redis，mongodb）
- 报警数据上传支持视频分组字段
- 报警数据上传支持图片或视频url字段（替代base64）
- 可配置自动清理报警数据（设置保存天数）
- 写入本地报警数据包含描述结果的json文件

## 7. 设备管理功能需求

### 7.1 摄像头管理
- 自定义添加摄像头
- 批量导入摄像头
- 批量转发功能
- 自启动转发功能
- ONVIF搜索功能
- ONVIF自动获取地址功能
- ONVIF获取设备信息功能
- ONVIF截屏功能

### 7.2 布控管理
- 自定义添加布控
- 批量布控功能
- 布控复制功能
- 布控轮巡功能
- 布控分页功能
- 快捷设置功能
- 多进程模式支持
- 布控日志查询
- 布控数量上限设置
- 通过复制批量为离线摄像头设置布控
- 布控目标选项配置
- 阈值选项配置

## 8. 系统集成功能需求

### 8.1 集群管理
- 接入xcnvs集群管理平台
- 支持查看和管理任意节点的摄像头/算法流/录像回放
- 适合公网部署管理分散内网节点

### 8.2 多进程支持
- 高级版多进程负载均衡算法
- 自动负载均衡模式
- 多进程环境下数据同步
- 多进程实时信息显示
- 业务算法所属进程显示
- 异常停止布控自动恢复

## 9. 开放接口功能需求

### 9.1 基础接口
- 添加摄像头/删除摄像头接口
- 查询软件基本信息接口
- 查询存储信息接口
- 重启软件/重启系统接口
- 手动抓拍图片和手动录像接口

### 9.2 算法接口
- 图片检测接口
- API类型基础算法接入
- API行为算法扩展
- 算法开放能力接口

### 9.3 安全验证
- 开放接口安全校验机制
- 模块间安全验证逻辑

## 10. 用户管理功能需求

### 10.1 用户权限
- 用户管理模块
- 区分管理员和普通用户
- 管理员可以增删改查用户

### 10.2 登录安全
- 账号信息公钥加密传输
- 验证码功能
- 连续登录失败锁定功能
- HOST白名单设置

## 11. 任务计划功能需求

### 11.1 定时任务
- 任务计划功能（定时执行布控任务）
- 定时执行视频流转发任务
- 定时轮巡任务
- 定时重启软件/系统
- 间隔扫描离线摄像头

### 11.2 计划类型
- 自动开启人脸识别类型
- 可配置计划任务参数

## 12. 显示与渲染功能需求

### 12.1 视频播放
- 分屏功能（1分屏，4分屏，9分屏，16分屏）
- 全屏播放功能
- 自适应转码视频分辨率
- h265转码器方案替代为web播放器

### 12.2 算法渲染
- 算法流动态编码功能
- 绘制中文名称支持
- 绘制多目标框（矩形、多边形）
- 自定义颜色/厚度/字体大小
- OSD贴图功能（支持中文内容）
- 用户自定义渲染目标
- 算法和FPS显示起点坐标设置
- 绘制线段自定义样式
- 关键点渲染支持（pose模型）

## 13. 配置管理功能需求

### 13.1 配置导入导出
- 在线导出配置/导入配置功能
- 在线导出日志功能
- 导出日志包含config.json,settings.json,config.ini

### 13.2 参数配置
- 可配置ps_effect_min_fps和pull_frequency
- 可配置中文提示词
- 可配置IP音柱报警参数
- 可配置多进程模式
- 开启调试模式功能
- 软件启动配置修改

## 14. 授权与安全功能需求

### 14.1 授权管理
- 机器授权码授权
- 加密锁授权
- 系统设备ID绑定授权
- 永久授权码支持

### 14.2 安全功能
- 算法模型加密功能
- 限制软件子模块服务对外访问权限
- 限制打印模型加密扩展参数

## 15. 性能优化功能需求

### 15.1 硬件加速
- TensorRT推理预处理优化
- 英伟达显卡多卡计算支持
- ARM架构硬件加速（RK、昇腾、Jetson）
- MPP视频硬解码加速
- RGA加速支持
- Neon指令集加速（ARM版本）
- Xcc硬件加速兼容库

### 15.2 算法优化
- 算法决策逻辑优化
- 模型实例复用技术
- 动态模型实例化和删除
- 自动调节布控数量
- 内存复用技术
- 解码内存复用技术
- 预处理性能优化

### 15.3 系统优化
- 自动负载均衡模式
- 模型缓存时长配置
- 间隔帧检测配置
- 间隔秒检测配置
- 自由竞争模式扩展

## 16. 系统环境与兼容性需求

### 16.1 操作系统支持
- Windows平台支持
- Ubuntu系统支持
- Linux系统支持

### 16.2 环境检测功能
- 端口占用检测
- 程序重开检测
- 推理设备支持检测
- 处理器支持检测
- 显卡支持检测

### 16.3 软件启动功能
- Windows平台软件自启动
- 软件启动配置修改
- 环境检测执行速度优化
- 启动器控制逻辑优化

## 17. 模型管理功能需求

### 17.1 模型格式支持
- engine格式模型上传
- rknn模型支持
- om模型支持
- TensorRT模型验证

### 17.2 模型加密功能
- 算法模型加密
- 模型试用时长设置
- 自定义编号支持
- 自动加密判断

### 17.3 模型实例管理
- 动态模型实例化
- 模型实例删除
- 模型实例复用
- 自定义并发数量设置
- 自适应模型分辨率

## 18. 数据导出与样本管理需求

### 18.1 样本导出功能
- 报警图片导出为labelme格式训练样本
- 不画框类型报警图片导出
- 报警数据导出为样本

### 18.2 配置导出功能
- 在线导出配置/导入配置
- 导出config.json,settings.json,config.ini
- 导出在线视频流和布控数据
- 可配置导出流媒体日志

## 19. 版本管理与升级需求

### 19.1 版本检测功能
- 新版本检测功能
- 新版本弹窗提示
- 版本升级包控制

### 19.2 离线升级功能
- 导入离线升级包更新版本
- 升级包版本兼容性控制
- 最低版本要求验证

## 20. 二次开发与扩展需求

### 20.1 算法扩展功能
- 动态库类型行为算法
- 兼容算法动态库
- 国产硬件算法兼容
- 用户自定义算法训练

### 20.2 开发接口扩展
- 扩展算法检测模式
- 二次开发报警接口
- 算法开放能力接口
- 流媒体开发免除
